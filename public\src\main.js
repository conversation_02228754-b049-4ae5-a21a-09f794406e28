// main.js
import { defineRoute, renderRoute, setDefaultRoute } from '../msec/framework/router.js';
import { nicknameScreen } from './ui/nicknameScreen.js';
import { lobbyScreen } from './ui/lobbyScreen.js';
import { gameScreen } from './ui/gameScreen.js';
import { GameDashboardScreen } from './ui/GameDashboardScreen.js';
import { connect } from './multiplayer/socket.js';
import { setState } from '../msec/framework/state.js';
import { StartScreen } from './ui/startScreen.js';

// Connect to WebSocket server - use current host's IP for cross-device compatibility
const wsHost = window.location.hostname === 'localhost' ? 'localhost' : window.location.hostname;
const wsUrl = `ws://${wsHost}:8080`;
console.log('[Main] Connecting to WebSocket:', wsUrl);
connect(wsUrl);

// Define routes
defineRoute('/', StartScreen);
defineRoute('/player', nicknameScreen);
defineRoute('/lobby', lobbyScreen);
defineRoute('/game', GameDashboardScreen);
// defineRoute('/game', gameScreen);

// Set default route for unmatched URLs
setDefaultRoute('/');

// Initial render
window.onload = () => {
  // Ensure there is a root element
  if (!document.getElementById('app')) {
    const appDiv = document.createElement('div');
    appDiv.id = 'app';
    document.body.appendChild(appDiv);
  }
  renderRoute();
};
