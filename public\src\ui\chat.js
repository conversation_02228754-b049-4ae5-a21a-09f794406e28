import { sendChat, subscribeToState } from '../multiplayer/sync.js';
import { getState } from '../../msec/framework/state.js';

let lastMessages = [];

export function chatUI(root) {
  // Chat container
  const chatDiv = document.createElement('div');
  chatDiv.className = 'chat-container';

  // Header
  const header = document.createElement('div');
  header.className = 'chat-header';
  header.textContent = 'Public Lobby Chat';
  chatDiv.appendChild(header);

  // Messages
  const msgList = document.createElement('div');
  msgList.className = 'chat-messages';
  chatDiv.appendChild(msgList);

  // Form
  const form = document.createElement('form');
  form.className = 'chat-form';
  const input = document.createElement('input');
  input.type = 'text';
  input.placeholder = 'Type a message...';
  input.maxLength = 100;
  input.tabIndex = 0;
  form.appendChild(input);

  const sendBtn = document.createElement('button');
  sendBtn.type = 'submit';
  sendBtn.textContent = 'Send';
  sendBtn.disabled = true;
  form.appendChild(sendBtn);

  chatDiv.appendChild(form);
  root.appendChild(chatDiv);

  // Enable send button if input has text
  input.addEventListener('input', () => {
    sendBtn.disabled = !input.value.trim();
  });

  // ✅ ONLY focus input if you click outside the input/form
  chatDiv.addEventListener('click', (e) => {
    if (!form.contains(e.target)) {
      input.focus();
    }
  });

  // ✅ Deep compare arrays of messages
  function areMessagesEqual(a, b) {
    if (!Array.isArray(a) || !Array.isArray(b)) return false;
    if (a.length !== b.length) return false;
    return a.every((m, i) => m.nickname === b[i].nickname && m.text === b[i].text);
  }

  // ✅ Only update DOM if chat content changed
  function renderMessages(messages) {
    if (areMessagesEqual(messages, lastMessages)) return;
    lastMessages = [...messages];

    const wasAtBottom = msgList.scrollTop + msgList.clientHeight >= msgList.scrollHeight - 10;

    // Clear and re-render
    msgList.innerHTML = '';
    const myNick = getState().nickname;
    messages.slice(-20).forEach(msg => {
      const msgDiv = document.createElement('div');
      msgDiv.className = 'chat-message';
      if (msg.nickname === myNick) msgDiv.classList.add('me');
      msgDiv.textContent = `${msg.nickname}: ${msg.text}`;
      msgList.appendChild(msgDiv);
    });

    if (wasAtBottom) {
      msgList.scrollTop = msgList.scrollHeight;
    }
  }

  // ✅ Prevent re-renders from breaking focus
  subscribeToState((state) => {
    if (state.chat && Array.isArray(state.chat)) {
      renderMessages(state.chat);
    }
  });

  // ✅ On submit, send message without re-rendering input
  form.addEventListener('submit', (e) => {
    e.preventDefault();
    const text = input.value.trim();
    if (!text) return;
    sendChat(text);
    input.value = '';
    sendBtn.disabled = true;
    // Focus remains here
  });
}
