// sync.js
// Handles game state sync via WebSocket
import { onMessage, send } from './socket.js';

let stateListeners = [];

export function subscribeToState(fn) {
  stateListeners.push(fn);
}

// Call this once to start listening for state updates from server
onMessage((msg) => {
  if (msg.type === 'state') {
    console.log('[Sync] Broadcasting state update to', stateListeners.length, 'listeners');
    if (msg.state.phase === 'game') {
      console.log('[Sync] Game state update:', {
        phase: msg.state.phase,
        playerCount: msg.state.players ? msg.state.players.length : 0,
        hasMap: !!msg.state.map
      });
    }
    stateListeners.forEach(fn => fn(msg.state));
  }
});

// Send player action to server
export function sendAction(action) {
  send({ type: 'action', action });
}

// Send chat message to server
export function sendChat(text) {
  send({ type: 'chat', text });
}
