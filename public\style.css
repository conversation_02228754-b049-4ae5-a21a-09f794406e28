/* body {
  font-family: Arial, sans-serif;
  background: #222;
  color: #fff;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

#app {
  margin-top: 32px;
}

.nickname-screen, .lobby-screen, .game-loading, .game-screen {
  background: #333;
  padding: 32px;
  border-radius: 8px;
  box-shadow: 0 2px 8px #000a;
  min-width: 320px;
  text-align: center;
}

/* Game Screen Styles */
#game-root {
  background: #222;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

/* DOM-based Game Board Styles - Cross-browser compatible */
.game-board {
  /* border: 2px solid #666; */
  display: block;
  margin: 0 auto;
  background: #0a131e;
  position: relative;
  /* Cross-browser pixelated rendering */
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: -webkit-crisp-edges;
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor;
  /* Performance optimizations */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: contents;
}

/* Game Tile Styles */
.game-tile {
  position: absolute;
  box-sizing: border-box;
  border: 1px solid rgba(102, 102, 102, 0.3);
  transition: background-color 0.1s ease;
}

.tile-empty {
  background-color: transparent;
}

.tile-wall {
  background-color: #8B4513;
  border: 1px solid #654321;
  box-shadow: inset 2px 2px 4px rgba(255, 255, 255, 0.2),
              inset -2px -2px 4px rgba(0, 0, 0, 0.3);
}

.tile-destructible {
  background-color: #DEB887;
  border: 1px solid #CD853F;
  background-image:
    linear-gradient(45deg, rgba(139, 69, 19, 0.1) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(139, 69, 19, 0.1) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(139, 69, 19, 0.1) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(139, 69, 19, 0.1) 75%);
  background-size: 8px 8px;
  background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
}

/* Game Object Styles */
.game-object {
  position: absolute;
  box-sizing: border-box;
}

/* Player Styles */
.game-object.player {
  border-radius: 4px;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 2px rgba(255, 255, 255, 0.3);
  transition: transform 0.1s ease;
}

.game-object.player:hover {
  transform: scale(1.05);
}

.player-nickname {
  font-family: Arial, sans-serif;
  font-weight: bold;
  white-space: nowrap;
  text-shadow:
    1px 1px 1px rgba(255, 255, 255, 0.8),
    -1px -1px 1px rgba(0, 0, 0, 0.3);
}

/* Bomb Styles */
.game-object.bomb {
  border-radius: 50%;
  font-family: Arial, sans-serif;
  box-shadow:
    0 3px 6px rgba(0, 0, 0, 0.4),
    inset 0 1px 3px rgba(255, 255, 255, 0.2);
  transform-origin: center;
  -webkit-transform-origin: center;
  -webkit-animation: bomb-pulse 0.4s ease-in-out infinite alternate;
  animation: bomb-pulse 0.4s ease-in-out infinite alternate;
}

/* Enhanced Explosion Styles */
.explosion-container {
  pointer-events: none;
}

.explosion-outer {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 100, 0, 0.9) 0%, rgba(255, 100, 0, 0.6) 70%, transparent 100%);
  border-radius: 50%;
  -webkit-animation: explosion-outer 0.3s ease-out infinite alternate;
  animation: explosion-outer 0.3s ease-out infinite alternate;
}

.explosion-inner {
  position: absolute;
  left: 18.75%;
  top: 18.75%;
  width: 62.5%;
  height: 62.5%;
  background: radial-gradient(circle, rgba(255, 200, 0, 0.95) 0%, rgba(255, 200, 0, 0.7) 70%, transparent 100%);
  border-radius: 50%;
  animation: explosion-inner 0.2s ease-out infinite alternate;
}

.explosion-center {
  position: absolute;
  left: 37.5%;
  top: 37.5%;
  width: 25%;
  height: 25%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 70%, transparent 100%);
  border-radius: 50%;
  animation: explosion-center 0.15s ease-out infinite alternate;
}

/* Powerup Styles */
.game-object.powerup {
  border-radius: 6px;
  font-family: Arial, sans-serif;
  transition: transform 0.2s ease;
  box-shadow:
    0 0 10px currentColor,
    inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

.game-object.powerup:hover {
  transform: scale(1.1);
}

/* FPS Counter Styles */
.fps-counter {
  font-family: Arial, sans-serif;
  user-select: none;
  pointer-events: none;
}

/* Enhanced Animations with cross-browser support */
@-webkit-keyframes bomb-pulse {
  0% {
    opacity: 0.8;
    -webkit-transform: scale(1);
    transform: scale(1);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4), inset 0 1px 3px rgba(255, 255, 255, 0.2);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1.08);
    transform: scale(1.08);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5), inset 0 2px 4px rgba(255, 255, 255, 0.3);
  }
}

@keyframes bomb-pulse {
  0% {
    opacity: 0.8;
    transform: scale(1);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4), inset 0 1px 3px rgba(255, 255, 255, 0.2);
  }
  100% {
    opacity: 1;
    transform: scale(1.08);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5), inset 0 2px 4px rgba(255, 255, 255, 0.3);
  }
}

@keyframes explosion-outer {
  0% {
    opacity: 0.9;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes explosion-inner {
  0% {
    opacity: 0.95;
    transform: scale(0.7);
  }
  100% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes explosion-center {
  0% {
    opacity: 0.9;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1.3);
  }
}

@keyframes powerup-pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 8px currentColor, inset 0 2px 4px rgba(255, 255, 255, 0.3);
  }
  100% {
    transform: scale(1.05);
    box-shadow: 0 0 20px currentColor, inset 0 3px 6px rgba(255, 255, 255, 0.4);
  }
}

/* Cross-browser performance optimizations */
.game-board * {
  will-change: transform, opacity;
  -webkit-will-change: transform, opacity;
}

.game-object {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
  /* Prevent text selection for better performance */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* Smooth animations across browsers */
  -webkit-transition: transform 0.1s ease;
  -moz-transition: transform 0.1s ease;
  -ms-transition: transform 0.1s ease;
  transition: transform 0.1s ease;
}

/* Chat Styles */
/* 
.chat-container {
  background: #444;
  border-radius: 8px;
  padding: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.chat-header {
  color: #4af;
  font-weight: bold;
  margin-bottom: 12px;
  text-align: center;
}

.chat-messages {
  background: #222;
  border: 1px solid #666;
  border-radius: 4px;
  height: 200px;
  overflow-y: auto;
  padding: 8px;
  margin-bottom: 12px;
  font-family: monospace;
  font-size: 14px;
}

.chat-message {
  margin-bottom: 4px;
  color: #ccc;
}

.chat-message.me {
  color: #4af;
  font-weight: bold;
}

.chat-form {
  display: flex;
  gap: 8px;
}

.chat-form input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #666;
  border-radius: 4px;
  background: #333;
  color: #fff;
  font-size: 14px;
}

.chat-form input:focus {
  outline: none;
  border-color: #4af;
}

.chat-form button {
  padding: 8px 16px;
  background: linear-gradient(90deg, #4af 60%, #6cf 100%);
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.chat-form button:hover:not(:disabled) {
  background: linear-gradient(90deg, #6cf 60%, #8ef 100%);
}

.chat-form button:disabled {
  background: #666;
  cursor: not-allowed;
} */

.game-root {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.game-map {
  display: grid;
  grid-template-rows: repeat(15, 32px);
  grid-template-columns: repeat(17, 32px);
  background: #222;
  border: 4px solid #555;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}
.game-map-abs {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  display: block;
}
.player, .bomb, .explosion, .powerup {
  position: absolute;
  pointer-events: none;
  transform: translate(-50%, -50%);
}

.map-row {
  display: contents;
}

.map-tile {
  width: 32px;
  height: 32px;
  box-sizing: border-box;
  border: 1px solid #333;
  background: #222;
}
.tile-wall {
  background: #666;
}
.tile-block {
  background: #b97a56;
}
.tile-empty {
  background: #222;
}

.player {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #4af;
  border: 2px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
  font-size: 14px;
  z-index: 2;
}
.player-0 { background: #4af; }
.player-1 { background: #fa4; }
.player-2 { background: #4fa; }
.player-3 { background: #f44; }

.bomb {
  position: absolute;
  width: 24px;
  height: 24px;
  background: #222;
  border: 3px solid #000;
  border-radius: 50%;
  z-index: 3;
  left: 4px;
  top: 4px;
}

.explosion {
  position: absolute;
  width: 32px;
  height: 32px;
  background: radial-gradient(circle, #ff0 60%, #f80 100%);
  opacity: 0.8;
  z-index: 4;
}

.powerup {
  position: absolute;
  width: 20px;
  height: 20px;
  left: 6px;
  top: 6px;
  border-radius: 4px;
  z-index: 2;
  background: #fff;
  border: 2px solid #0cf;
}
.powerup-bomb_up { background: #0cf; }
.powerup-flame_up { background: #f80; }
.powerup-speed_up { background: #0f8; }

/* --- Modern Chat Lobby Styles --- */
/* .chat-container {
  background: rgba(34, 34, 34, 0.85);
  border: 1.5px solid #3af;
  border-radius: 14px;
  width: 544px;
  max-width: 95vw;
  padding: 0 0 8px 0;
  position: fixed;
  left: 50%;
  bottom: 36px;
  transform: translateX(-50%);
  z-index: 100;
  box-shadow: 0 8px 32px 0 rgba(0,0,0,0.35), 0 1.5px 8px #3af2;
  backdrop-filter: blur(4px);
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.chat-header {
  padding: 10px 20px 6px 20px;
  font-size: 17px;
  font-weight: bold;
  color: #3af;
  letter-spacing: 1px;
  border-bottom: 1px solid #3af4;
  text-align: left;
  background: transparent;
  border-radius: 14px 14px 0 0;
}
.chat-messages {
  height: 140px;
  overflow-y: auto;
  background: transparent;
  border-radius: 0 0 8px 8px;
  padding: 10px 18px 4px 18px;
  margin-bottom: 0;
  font-size: 15px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.chat-message {
  background: rgba(60, 60, 80, 0.7);
  border-radius: 8px;
  padding: 4px 10px;
  margin-bottom: 0;
  color: #fff;
  word-break: break-word;
  box-shadow: 0 1px 3px #0002;
  font-size: 15px;
  max-width: 90%;
  align-self: flex-start;
}
.chat-message.me {
  background: rgba(58, 170, 255, 0.18);
  color: #3af;
  align-self: flex-end;
}
.chat-form {
  display: flex;
  gap: 8px;
  padding: 8px 16px 0 16px;
  align-items: center;
}
.chat-form input {
  flex: 1;
  padding: 7px 12px;
  border-radius: 6px;
  border: 1.5px solid #3af4;
  background: #181c22;
  color: #fff;
  font-size: 15px;
  outline: none;
  transition: border 0.2s;
}
.chat-form input:focus {
  border: 1.5px solid #3af;
  background: #222a33;
}
.chat-form button {
  background: linear-gradient(90deg, #3af 60%, #4af 100%);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 7px 18px;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 1px 4px #3af2;
  transition: background 0.2s, box-shadow 0.2s;
}
.chat-form button:hover {
  background: linear-gradient(90deg, #4af 60%, #3af 100%);
  box-shadow: 0 2px 8px #3af3;
} */

.lobby-countdown {
  font-size: 18px;
  margin-top: 12px;
  color: #F6265A;
}

.player-list {
  list-style: none;
  padding: 0;
  margin: 8px 0 0 0;
}
.player-list li {
  margin: 2px 0;
}
.player-nick {
  display: block;
  font-size: 13px;
  font-weight: bold;
  text-shadow: 0 1px 2px #000a;
}
.player-lives {
  display: block;
  font-size: 15px;
  color: #f44;
  letter-spacing: 2px;
  margin-top: 1px;
  text-shadow: 0 1px 2px #000a;
}
.player-dead {
  opacity: 0.4;
  filter: grayscale(0.7);
}

/* New Design */
Body {
    background-color: #07131F;
    font-family: 'EngraversGothic', 'Arial', 'Helvetica', sans-serif;
    margin: 0 auto;
    padding: 0;
    overflow-x: hidden;
    overflow-y: hidden;
}

@media screen and (max-width: 600px) {
    body {
        overflow-x: hidden;
        overflow-y: auto;
    }
    
}

@font-face {
    font-family: 'EngraversGothic';
    src: url('/static/fonts/Engravers.otf') format('opentype'),
         url('/static/fonts/Engravers.ttf') format('truetype'); /* Add TTF as fallback */
    font-weight: normal;
    font-style: normal;
    font-display: swap; /* Add this for better font loading performance */
}

::selection {
  background-color: #F6265A;
  color: white; /* or any text color that looks good on the pink */
}

.splash-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
}

.splash-screen{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.character-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.splash-screen .logo-container img{
    width: 70vw;
    height: auto;
    animation: fadeIn 2s ease-in-out;
}

.splash-screen .menu{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: -70px;
}

.splash-screen .menu a{
    font-family: 'EngraversGothic', sans-serif;
    text-transform: uppercase;
    letter-spacing: 9px;
    color: white;
    font-family: 'EngraversGothic', serif;
    text-decoration: none;
    margin-bottom: 14px;
    font-size: 1.3rem;
    opacity: 0.75;
    transition: opacity 0.3s ease-in-out;
}

.splash-screen .menu a:hover{
    opacity: 1;
}

.character-container .heading{
    margin-top: 5vh;
}

.img-header{
    width: 35vw;
    height: auto;
    max-width: 600px;
    margin-bottom: 20px;
    min-width: 500px;
}

.character-selection{
    display: flex;
    /* flex-direction: column; */
    align-items: center;
    justify-content: center;
    width: 70vw;
}

.nickname-container, .lobby-container{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
}

.lobby-container{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
}

.nickname-container .nickname-form{
    background-color: #c5c5c5ba;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
}

.lobby-container .lobby-screen{
      background-color: #c5c5c5ba;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
      width: 50vw;
    height: 30vh;
}

.nickname-container .nickname-form input{
  padding: 10px;
  outline: none;
  min-width: 200px;
  font-family: 'EngraversGothic', serif;
  border-radius: 10px 0px 0px 10px;
  border: none;
  text-transform: uppercase;
}

.nickname-container .nickname-form button{
  padding: 10px 15px;
  border-radius: 0px 10px 10px 0px;
  border: none;
  font-family: 'EngraversGothic', serif;
  background-color: #F6265A;
  color: white;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.nickname-container .nickname-form button:hover{
  background-color: #cf1f4b;
}

.links-demo{
    display: flex;
    gap: 10px;
}

.links-demo a{
    /* color: #F6265A !important; */
    color: white !important;
    text-decoration: none;
    margin-right: 10px;
    text-transform: uppercase;
    opacity: 0.5;
    transition: opacity 0.2s ease;
}

.links-demo a:hover{
  opacity: 1;
}

.main-menu{
    position: relative;
}

/* Game Dashboard Page */
.menu-container {
    display: flex;
    align-items: stretch;
    height: 46px;
    /* position: absolute; */
}

.menu-container .left-border-shape{
    margin-right: -1px;
}

.left-border-shape img{
  height: 45.9px;
}

.right-border-shape img{
  height: 45.9px;
}

.menu-container .right-border-shape{
    margin-left: -1px;
    transform: scale(-1, 1);
}

.menu-content {
    background: white;
    height: 45.9px;
    min-width: 200px;
    width: 55vw;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.menu-container .menu-content .left-side{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 50%;
    margin-top: -2px;
}

.menu-container .menu-content .left-side a{
    text-transform: uppercase;
    text-decoration: none;
    color: black;
    letter-spacing: 5px;
    font-size: 0.9rem;
}

.menu-container .menu-content .left-side .seperator{
    background-color: #D6D6D6;
    width: 2px;
    height: 20px;
    font-size: 0.9rem;
}

.player-font{
    font-family: 'EngraversGothic', serif;
    text-transform: uppercase;
    letter-spacing: 5px;
    color: black;
    /* margin-left: 10px; */
}

.hearts{
    display: flex;
    align-items: center;
    justify-content: left;
    margin-top: 3px;
}

.hearts .heart{
    width: 25px;
    height: 25px;
    background-image: url('./static/images/Heart.png'); /* Updated path */
    background-size: cover;
    /* margin-right: 5px; */
}

.menu-container .menu-content .right-side{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 27%;
    margin-top: -2px;
}

.stat .flex{
    display: flex;
    align-items: center;
}

.stat .flex img{
    width: 30px;
    height: auto;
    margin-right: 9px;
}

/* Game Area */
.dashboard{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100vw;
    margin-top: 25px;
}

.chat-container{
    /* width: 15vw; */
    width: 220px;
    height: 91vh;
    display: flex;
    align-items: end;
    /* position: relative; */
    transition: margin-left 0.3s ease-in-out;
    position: absolute;
    bottom: 0;
    z-index: 1;
    left: 0;
}

.chat-sidebar{
    width: 220px;
    height: 90vh;
    background-color: #ffffffd2;
    border-radius: 0px 20px 0px 0px ;
    transition: margin-left 0.3s ease-in-out;
        backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px); /* Safari support */
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  max-height: 570px;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  margin-top: 20px;
}

.chat-message .player-font{
    font-family: 'EngraversGothic', serif;
    text-transform: uppercase;
    letter-spacing: 5px;
    color: #f6265a;
    font-size: 0.9rem;
}

.chat-message p{
    font-family: 'Montserrat', sans-serif;
    font-size: 1rem;
    margin-top: 2px;
    margin-bottom: 0px;
}

/* Target the vertical scrollbar */
.chat-messages::-webkit-scrollbar {
  width: 8px; /* Adjust width */
}

/* Remove the up/down arrows (default behavior) */
.chat-messages::-webkit-scrollbar-button {
  display: none;
}

/* Scrollbar track (background) */
.chat-messages::-webkit-scrollbar-track {
  background: transparent; /* or any light color */
}

/* Scrollbar thumb (the draggable part) */
.chat-messages::-webkit-scrollbar-thumb {
  background-color: #f6265ae2;
  border-radius: 10px;
  max-width: 2px;
}

/* Optional: On hover */
.chat-messages::-webkit-scrollbar-thumb:hover {
  background-color: #d01e4c;
}


.chat-container-inner {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.chat-message {
  /* background-color: white; */
  padding: 6px 10px;
  border-radius: 6px;
  /* box-shadow: 0 1px 2px rgba(0,0,0,0.1); */
}

.chat-drawer{
    width: 180px;
    height: 30px;
    background-color: #ffffffd2;
    border-radius: 60px 60px 00px 0px;
    transform: rotate(90deg);
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 200px;
    margin-left: 145px;
    top: 0;
    left: 0;
    cursor: pointer;
    transition: margin-left 0.3s ease-in-out;
}

.chat-drawer p{
    color: black;
    transition: all 0.3s ease-in-out;
}

.chat-drawer p:hover{
    color: #F6265A;
}

.closed{
    margin-left: -220px;
    transition: margin-left 0.3s ease-in-out;
}

.game-area{
    width: 70vw;
    margin-left: 13vw;
    aspect-ratio: 16 / 9; /* or 4 / 3, 1 / 1, etc. */
    /* height: 86vh; */
    /* background-color: darkgray; */
}

.player-stats-sidebar{
    width: 15vw;
    height: 91vh;
    background-color: #ffffffd2;
    /* position: fixed; */
    bottom: 0;
    border-radius: 20px 0px 0px 20px ;
}

.player-stats-down{
    width: 100vw;
    min-height: 100px;
    background-color: #ffffffd2;
    /* position: fixed; */
    bottom: 0;
    border-radius: 20px 20px 0px 0px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: absolute;
}

.player-stats-down .player-stat{
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.player-stats-down .player-stats{
    margin-left: 20px;
}

@media  screen and (min-width: 1200px) {
    .player-stats-down{
        display: none;
    }
}

.player-stats-sidebar .player-stat{
    padding: 15px 35px 15px 30px;
}

.player-stat .player-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-top: 10px;
}

.player-stat .player-stats .stat {
    height: 40px;
    font-weight: normal;
}

.player-stat .player-stats .stat {
    display: flex;
    align-items: center;
}

.player-stat .player-stats .stat .flex img {
    width: 23px;
    height: auto;
    margin-right: 20px;
}
.player-stat .player-stats .stat h3 {
    font-weight: normal;
}

.player-name .player-font{
    font-size: 1rem;
    font-weight: normal;
}

.player-name .hearts{
    margin-top: -10px;
    margin-left: 20px;
}

.input-form{
    width: 100%;
    background-color: white;
    height: 60px;
    padding: 5px;
    box-sizing: border-box;
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    border-radius: 20px 0px 0px 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: 0px;
}

.input-form form {
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-form form input{
    border: none;
    background-color: transparent;
    outline: none;
    font-family: 'Montserrat', sans-serif;
    font-size: 0.7rem;
    width: 90%;
}

.input-form form button{
    border: none;
    background-color: transparent;
}

.input-form form img{
    width: 20px;
    opacity: 0.8;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
}

.input-form form img:hover{
    opacity: 1;
}

@media screen and (max-width: 900px) {
    .splash-screen .menu{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 30px;
    }
    .splash-screen .logo-container img{
        width: 90vw;
        height: auto;
        animation: fadeIn 2s ease-in-out;
    }
}

@media screen and (max-width: 1200px) {
    .menu-content {
        width: 80vw;
    }
    .player-stats-sidebar{
        display: none;
    }
    .game-area {
        width: 70vw;
        margin-left: 15vw;
        aspect-ratio: 16 / 9;
        /* height: 86vh; */
        background-color: darkgray;
    }
}

@media screen and (max-width: 860px) {
    .menu-container .menu-content .left-side {
        width: 40%;
    }
    .menu-container .menu-content .right-side {
        width: 35%;
    }
    .menu-container .menu-content .left-side p{
        display: none;
    }
    .player-stats-down .player-stat {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: column;
    }
    .player-stats-down {
        min-height: 250px;
    }
}

@media screen and (max-width: 700px) {
    .menu-container .menu-content .left-side {
        width: 50%;
    }
    .menu-container .menu-content .right-side {
        width: 40%;
    }
}

@media screen and (max-width: 600px) {
    .menu-container .menu-content .left-side {
        width: 55%;
    }
    .menu-container .menu-content .right-side {
        width: 40%;
    }

    .stat .flex img {
        width: 20px;
        height: auto;
        margin-right: 9px;
    }

    .stat .flex h3 {
        font-size: 0.9rem;
    }
}

@media screen and (max-width: 400px) {
    .menu-container .menu-content .left-side {
        width: 55%;
    }

    .menu-container .menu-content .left-side a{
        display: none;
    }
    .menu-container .menu-content .left-side .seperator{
        display: none;
    }
    .menu-container .menu-content .right-side {
        width: 60%;
    }
    .menu-content {
        width: 70vw;
    }
    .stat .flex img {
        width: 20px;
        height: auto;
        margin-right: 9px;
    }

    .stat .flex h3 {
        font-size: 0.9rem;
    }
}