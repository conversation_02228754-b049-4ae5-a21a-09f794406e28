<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Game Debug - Bomberman DOM</title>
  <link rel="stylesheet" href="style.css">
  <style>
    .debug-container {
      background: #333;
      padding: 32px;
      border-radius: 8px;
      margin: 32px auto;
      max-width: 800px;
      font-family: monospace;
    }
    .debug-section {
      background: #444;
      padding: 16px;
      border-radius: 6px;
      margin-bottom: 16px;
    }
    .debug-section h3 {
      color: #4af;
      margin-top: 0;
    }
    .state-display {
      background: #222;
      border: 1px solid #666;
      padding: 12px;
      border-radius: 4px;
      white-space: pre-wrap;
      font-size: 12px;
      max-height: 300px;
      overflow-y: auto;
    }
    .action-buttons {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      margin-top: 10px;
    }
    .action-buttons button {
      padding: 8px 12px;
      background: #666;
      color: #fff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }
    .action-buttons button:hover {
      background: #777;
    }
    .status {
      padding: 8px 12px;
      border-radius: 4px;
      margin-bottom: 10px;
      font-weight: bold;
    }
    .status.connected { background: #2d5a2d; color: #90ff90; }
    .status.disconnected { background: #5a2d2d; color: #ff9090; }
    .status.connecting { background: #5a5a2d; color: #ffff90; }
  </style>
</head>
<body>
  <div class="debug-container">
    <h2>🔧 Game Debug Console</h2>
    
    <div class="debug-section">
      <h3>Connection Status</h3>
      <div id="connection-status" class="status disconnected">Disconnected</div>
      <div class="action-buttons">
        <button onclick="connectToServer()">Connect to Server</button>
        <button onclick="disconnectFromServer()">Disconnect</button>
        <button onclick="testGameRoute()">Test /game Route</button>
      </div>
    </div>
    
    <div class="debug-section">
      <h3>Current Game State</h3>
      <div id="game-state" class="state-display">No state data</div>
      <div class="action-buttons">
        <button onclick="refreshState()">Refresh State</button>
        <button onclick="clearState()">Clear State</button>
        <button onclick="simulateGameState()">Simulate Game State</button>
      </div>
    </div>
    
    <div class="debug-section">
      <h3>Test Actions</h3>
      <div class="action-buttons">
        <button onclick="sendTestNickname()">Send Test Nickname</button>
        <button onclick="sendMoveAction('up')">Move Up</button>
        <button onclick="sendMoveAction('down')">Move Down</button>
        <button onclick="sendMoveAction('left')">Move Left</button>
        <button onclick="sendMoveAction('right')">Move Right</button>
        <button onclick="sendBombAction()">Place Bomb</button>
      </div>
    </div>
    
    <div class="debug-section">
      <h3>Navigation Test</h3>
      <div class="action-buttons">
        <button onclick="navigateTo('/')">Go to Home</button>
        <button onclick="navigateTo('/lobby')">Go to Lobby</button>
        <button onclick="navigateTo('/game')">Go to Game</button>
        <button onclick="window.location.reload()">Reload Page</button>
      </div>
    </div>
    
    <div class="debug-section">
      <h3>Console Output</h3>
      <div id="console-output" class="state-display" style="height: 200px;"></div>
      <div class="action-buttons">
        <button onclick="clearConsole()">Clear Console</button>
      </div>
    </div>
  </div>

  <script type="module">
    let socket = null;
    let gameState = {};
    
    // Console capture
    const consoleOutput = document.getElementById('console-output');
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    
    function logToConsole(type, ...args) {
      const timestamp = new Date().toLocaleTimeString();
      const message = `[${timestamp}] ${type.toUpperCase()}: ${args.join(' ')}\n`;
      consoleOutput.textContent += message;
      consoleOutput.scrollTop = consoleOutput.scrollHeight;
    }
    
    console.log = (...args) => {
      logToConsole('log', ...args);
      originalLog.apply(console, args);
    };
    
    console.error = (...args) => {
      logToConsole('error', ...args);
      originalError.apply(console, args);
    };
    
    console.warn = (...args) => {
      logToConsole('warn', ...args);
      originalWarn.apply(console, args);
    };
    
    // Global functions for buttons
    window.connectToServer = () => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        console.log('Already connected');
        return;
      }
      
      socket = new WebSocket('ws://localhost:8080');
      updateConnectionStatus('connecting');
      
      socket.onopen = () => {
        console.log('Connected to WebSocket server');
        updateConnectionStatus('connected');
      };
      
      socket.onmessage = (event) => {
        const msg = JSON.parse(event.data);
        console.log('Received message:', msg);
        
        if (msg.type === 'state') {
          gameState = msg.state;
          updateGameState();
        }
      };
      
      socket.onclose = () => {
        console.log('WebSocket connection closed');
        updateConnectionStatus('disconnected');
      };
      
      socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        updateConnectionStatus('disconnected');
      };
    };
    
    window.disconnectFromServer = () => {
      if (socket) {
        socket.close();
        socket = null;
      }
    };
    
    window.testGameRoute = () => {
      window.location.href = '/game';
    };
    
    window.refreshState = () => {
      updateGameState();
    };
    
    window.clearState = () => {
      gameState = {};
      updateGameState();
    };
    
    window.simulateGameState = () => {
      gameState = {
        phase: 'game',
        players: [
          { id: 'test1', nickname: 'TestPlayer1', x: 32, y: 32 },
          { id: 'test2', nickname: 'TestPlayer2', x: 64, y: 64 }
        ],
        map: [
          [1, 1, 1, 1, 1],
          [1, 0, 2, 0, 1],
          [1, 2, 1, 2, 1],
          [1, 0, 2, 0, 1],
          [1, 1, 1, 1, 1]
        ],
        bombs: [
          { x: 96, y: 96, timer: 3000 }
        ],
        explosions: [],
        powerups: []
      };
      updateGameState();
      console.log('Simulated game state created');
    };
    
    window.sendTestNickname = () => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        const msg = { type: 'nickname', nickname: 'DebugPlayer' };
        socket.send(JSON.stringify(msg));
        console.log('Sent nickname:', msg);
      } else {
        console.warn('Not connected to server');
      }
    };
    
    window.sendMoveAction = (direction) => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        const msg = { type: 'action', action: { type: 'move', dir: direction } };
        socket.send(JSON.stringify(msg));
        console.log('Sent move action:', direction);
      } else {
        console.warn('Not connected to server');
      }
    };
    
    window.sendBombAction = () => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        const msg = { type: 'action', action: { type: 'bomb' } };
        socket.send(JSON.stringify(msg));
        console.log('Sent bomb action');
      } else {
        console.warn('Not connected to server');
      }
    };
    
    window.navigateTo = (path) => {
      console.log('Navigating to:', path);
      window.location.href = path;
    };
    
    window.clearConsole = () => {
      consoleOutput.textContent = '';
    };
    
    function updateConnectionStatus(status) {
      const statusEl = document.getElementById('connection-status');
      statusEl.className = `status ${status}`;
      statusEl.textContent = status.charAt(0).toUpperCase() + status.slice(1);
    }
    
    function updateGameState() {
      const stateEl = document.getElementById('game-state');
      stateEl.textContent = JSON.stringify(gameState, null, 2);
    }
    
    // Initialize
    console.log('Game debug console initialized');
    updateGameState();
  </script>
</body>
</html>
