const routes = {};
let defaultRoute = '/';

export function defineRoute(path, component) {
    routes[path] = component;
}

export function setDefaultRoute(path) {
    defaultRoute = path;
}

export function navigate(path) {
    window.history.pushState({}, path, window.location.origin + path);
    renderRoute();
}

export function renderRoute() {
    const path = window.location.pathname;
    const component = routes[path];
    if (component) {
        component();
    } else {
        // If no route matches, navigate to default route
        console.warn(`No route found for ${path}, redirecting to ${defaultRoute}`);
        if (path !== defaultRoute && routes[defaultRoute]) {
            navigate(defaultRoute);
        }
    }
}

// Handle browser back/forward buttons
window.onpopstate = renderRoute;

// Intercept clicks on anchor tags for SPA navigation
document.addEventListener('click', (e) => {
    // Check if the clicked element is an anchor tag or inside one
    const anchor = e.target.closest('a[href]');
    if (!anchor) return;
    
    const href = anchor.getAttribute('href');
    
    // Handle external links (open in new tab/window)
    if (href.startsWith('http://') || href.startsWith('https://') || href.startsWith('//')) {
        // For external links, check if they should open in new tab
        if (!anchor.hasAttribute('target')) {
            anchor.setAttribute('target', '_blank');
            anchor.setAttribute('rel', 'noopener noreferrer');
        }
        return; // Let the browser handle external links
    }
    
    // Handle internal links
    if (href.startsWith('/')) {
        e.preventDefault();
        navigate(href);
    }
});
