// performanceEngine.js
// High-performance game engine for 60fps Bomberman rendering

import { renderGame } from './renderer.js';

class PerformanceEngine {
  constructor() {
    this.isRunning = false;
    this.lastFrameTime = 0;
    this.targetFps = 60;
    this.frameInterval = 1000 / this.targetFps;
    this.gameState = null;
    this.renderContainer = null;
    this.onStateUpdate = null;
    
    // Performance tracking
    this.frameCount = 0;
    this.fpsStartTime = 0;
    this.currentFps = 0;
    this.frameDrops = 0;
    this.lastPerformanceLog = 0;
    
    // Optimization flags
    this.enablePerformanceLogging = true;
    this.enableFrameDropDetection = true;
    
    // Bind methods
    this.gameLoop = this.gameLoop.bind(this);
  }

  // Initialize the game engine
  init(renderContainer, onStateUpdate = null) {
    this.renderContainer = renderContainer;
    this.onStateUpdate = onStateUpdate;
    
    // Setup performance monitoring
    if (this.enablePerformanceLogging) {
      this.setupPerformanceMonitoring();
    }
    
    console.log('[PerformanceEngine] Initialized with 60fps target');
  }

  // Setup performance monitoring tools
  setupPerformanceMonitoring() {
    // Monitor paint and layout events
    if (window.performance && window.performance.mark) {
      window.performance.mark('game-engine-init');
    }
    
    // Setup frame drop detection
    if (this.enableFrameDropDetection) {
      this.setupFrameDropDetection();
    }
  }

  // Setup frame drop detection
  setupFrameDropDetection() {
    let lastTime = performance.now();
    const checkFrameDrops = () => {
      const now = performance.now();
      const delta = now - lastTime;
      
      // If frame took longer than 20ms (50fps), it's a drop
      if (delta > 20 && this.isRunning) {
        this.frameDrops++;
        console.warn(`[PerformanceEngine] Frame drop detected: ${delta.toFixed(2)}ms`);
      }
      
      lastTime = now;
      if (this.isRunning) {
        requestAnimationFrame(checkFrameDrops);
      }
    };
    
    requestAnimationFrame(checkFrameDrops);
  }

  // Start the game loop
  start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.lastFrameTime = performance.now();
    this.fpsStartTime = this.lastFrameTime;
    this.frameCount = 0;
    this.frameDrops = 0;
    this.lastPerformanceLog = this.lastFrameTime;
    
    console.log('[PerformanceEngine] Starting 60fps game loop');
    
    // Mark performance start
    if (window.performance && window.performance.mark) {
      window.performance.mark('game-loop-start');
    }
    
    requestAnimationFrame(this.gameLoop);
  }

  // Stop the game loop
  stop() {
    this.isRunning = false;
    
    // Log final performance stats
    this.logPerformanceStats();
    
    console.log('[PerformanceEngine] Stopped game loop');
  }

  // Update game state from server
  updateState(newState) {
    this.gameState = newState;
  }

  // Main game loop optimized for 60fps
  gameLoop(currentTime) {
    if (!this.isRunning) return;

    // Mark frame start for performance monitoring
    if (window.performance && window.performance.mark) {
      window.performance.mark('frame-start');
    }

    // Calculate delta time
    const deltaTime = currentTime - this.lastFrameTime;
    
    // Only render if enough time has passed (60fps = ~16.67ms per frame)
    if (deltaTime >= this.frameInterval) {
      // Track performance
      this.frameCount++;
      const elapsed = currentTime - this.fpsStartTime;
      
      // Update FPS counter every second
      if (elapsed >= 1000) {
        this.currentFps = Math.round((this.frameCount * 1000) / elapsed);
        
        // Log performance every 5 seconds
        if (currentTime - this.lastPerformanceLog >= 5000) {
          this.logPerformanceStats();
          this.lastPerformanceLog = currentTime;
        }
        
        this.frameCount = 0;
        this.fpsStartTime = currentTime;
      }

      // Update game logic if callback provided
      if (this.onStateUpdate && this.gameState) {
        this.onStateUpdate(this.gameState, deltaTime);
      }

      // Render the game efficiently
      if (this.gameState && this.renderContainer) {
        try {
          renderGame(this.gameState, this.renderContainer);
        } catch (error) {
          console.error('[PerformanceEngine] Render error:', error);
        }
      }

      this.lastFrameTime = currentTime;
    }

    // Mark frame end for performance monitoring
    if (window.performance && window.performance.mark) {
      window.performance.mark('frame-end');
      window.performance.measure('frame-duration', 'frame-start', 'frame-end');
    }

    // Continue the loop
    requestAnimationFrame(this.gameLoop);
  }

  // Log performance statistics
  logPerformanceStats() {
    const stats = this.getPerformanceMetrics();
    
    if (stats.fps < 55) {
      console.warn(`[PerformanceEngine] Performance warning:`, stats);
    } else {
      console.log(`[PerformanceEngine] Performance stats:`, stats);
    }
  }

  // Get comprehensive performance metrics
  getPerformanceMetrics() {
    const metrics = {
      fps: this.currentFps,
      frameDrops: this.frameDrops,
      isRunning: this.isRunning,
      targetFps: this.targetFps
    };

    // Add browser performance metrics if available
    if (window.performance && window.performance.getEntriesByType) {
      const measures = window.performance.getEntriesByType('measure');
      const frameMeasures = measures.filter(m => m.name === 'frame-duration');
      
      if (frameMeasures.length > 0) {
        const avgFrameTime = frameMeasures.reduce((sum, m) => sum + m.duration, 0) / frameMeasures.length;
        metrics.avgFrameTime = Math.round(avgFrameTime * 100) / 100;
        metrics.targetFrameTime = this.frameInterval;
      }
    }

    return metrics;
  }

  // Force a performance measurement
  measurePerformance() {
    return this.getPerformanceMetrics();
  }
}

// Export singleton instance
export const performanceEngine = new PerformanceEngine();
